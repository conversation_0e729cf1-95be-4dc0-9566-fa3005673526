# 🤖 CODY - Advanced Context-Aware AI Coding Assistant

CODY is an advanced, context-aware, AI-powered CLI agent for code assistance that supports natural language processing, multi-language coding, autonomous debugging, and advanced file handling.

## ✨ Features

### 🧠 Core Capabilities
- **Natural Language Processing**: Understands commands in English, Hindi, and mixed languages
- **Multi-threaded Execution**: Concurrent task handling for better performance
- **Context-Aware Operations**: Maintains conversation context and project understanding
- **Autonomous Debugging**: Automatic error detection and fixing suggestions
- **Predictive Prefetching**: Smart caching and suggestion system

### 🔧 Advanced Code Operations
- **AST-based Code Analysis**: Deep code structure understanding
- **Multi-language Support**: Python, JavaScript, TypeScript, Java, C++, Go, Rust
- **Smart Code Search**: Regex and semantic search capabilities
- **Code Refactoring**: Context-aware code improvements
- **Test Generation**: Automated unit test creation

### 🌐 Web Integration
- **Real-time Web Search**: Stack Overflow, GitHub, official documentation
- **RAG Integration**: Retrieval Augmented Generation for better responses
- **Documentation Lookup**: Instant access to programming resources

### 🛠️ Development Tools
- **Git Integration**: Full workflow support with intelligent staging
- **Terminal Integration**: Cross-platform command execution
- **File Operations**: Advanced file handling with fuzzy matching
- **Static Analysis**: Code quality assessment and suggestions

## 🚀 Quick Start

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd CODY
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your API keys
```

4. **Run CODY:**
```bash
python agent.py
```

### Environment Variables

Create a `.env` file with the following:

```env
# DeepSeek API (required)
DEEPSEEK_API_KEY=your_deepseek_api_key

# Gemini API (optional, for enhanced NLP)
GEMINI_API_KEY=your_gemini_api_key

# Web search (optional)
SERPAPI_KEY=your_serpapi_key
```

## 📖 Usage Examples

### Natural Language Commands

```bash
# Create files
"Mujhe ek Python login system chahiye"
"Create a JavaScript function for API calls"

# Debug code
"Fix the error in main.py"
"Debug this TypeError in my code"

# Search and analyze
"Search for authentication examples"
"Analyze the complexity of this function"

# Refactor code
"Improve the performance of this code"
"Extract this into smaller functions"
```

### Command Reference

| Command | Description |
|---------|-------------|
| `/add <file>` | Add file to context |
| `/search <query>` | Web search for programming help |
| `/debug <file>` | Analyze and debug file |
| `/analyze <file>` | Perform code analysis |
| `/refactor <file>` | Suggest code improvements |
| `/test <file>` | Generate unit tests |
| `/help` | Show all commands |

## 🏗️ Architecture

### Core Modules

```
core/
├── nlp_processor.py      # Natural language understanding
├── code_analyzer.py      # AST parsing and analysis
├── autonomous_debugger.py # Error detection and fixing
├── web_search_rag.py     # Web search and RAG
└── task_manager.py       # Multi-threading and caching
```

### Workflow

1. **Execute**: Perform the requested action
2. **Analyze**: Examine results and context
3. **Plan**: Determine next steps based on analysis
4. **Execute**: Continue with iterative improvements

## 🔧 Advanced Configuration

### Performance Tuning

```python
# In agent.py, adjust these constants:
MAX_CONCURRENT_TASKS = 4        # Concurrent task limit
CACHE_SIZE_MB = 100            # Cache size
ENABLE_PREDICTIVE_PREFETCHING = True  # Smart prefetching
```

### Language Support

CODY supports multiple programming languages with specialized handling:

- **Python**: Full AST analysis, pylint integration
- **JavaScript/TypeScript**: Syntax analysis, npm integration
- **Java**: Class structure analysis
- **C/C++**: Header analysis, compilation support
- **Go**: Module analysis, go tools integration
- **Rust**: Cargo integration, ownership analysis

## 🧪 Testing

Run the test suite:

```bash
# Unit tests
pytest tests/

# Integration tests
pytest tests/integration/

# Performance tests
pytest tests/performance/
```

## 📊 Performance Metrics

CODY tracks and optimizes:

- Task execution times
- Cache hit rates
- Context usage efficiency
- Memory consumption
- Response accuracy

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run code formatting
black .
isort .

# Run linting
pylint core/ agent.py
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- DeepSeek AI for the powerful language model
- OpenAI for the API standards
- The open-source community for various tools and libraries

## 📞 Support

For support and questions:

- Create an issue on GitHub
- Check the documentation
- Join our community discussions

---

**CODY** - Making coding smarter, faster, and more intuitive! 🚀
