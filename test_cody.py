#!/usr/bin/env python3

"""
Test script for CODY Agent
Verifies that all components are working correctly
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test core imports
        import json
        import re
        import subprocess
        import threading
        import asyncio
        print("✓ Standard library imports successful")
        
        # Test third-party imports
        from rich.console import Console
        from prompt_toolkit import PromptSession
        print("✓ UI library imports successful")
        
        # Test optional imports
        try:
            from thefuzz import fuzz
            print("✓ Fuzzy matching available")
        except ImportError:
            print("⚠ Fuzzy matching not available")
        
        try:
            import tree_sitter
            print("✓ Tree-sitter available")
        except ImportError:
            print("⚠ Tree-sitter not available")
        
        try:
            import requests
            from bs4 import BeautifulSoup
            print("✓ Web search dependencies available")
        except ImportError:
            print("⚠ Web search dependencies not available")
        
        # Test custom modules
        try:
            from core.nlp_processor import NLPProcessor
            from core.code_analyzer import CodeAnalyzer
            from core.autonomous_debugger import AutonomousDebugger
            from core.web_search_rag import WebSearchRAG
            from core.task_manager import MultiThreadedTaskManager
            print("✓ Custom modules available")
            return True
        except ImportError as e:
            print(f"⚠ Custom modules not available: {e}")
            return False
            
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_core_modules():
    """Test that core modules can be instantiated."""
    print("\nTesting core modules...")
    
    try:
        from core.nlp_processor import NLPProcessor
        nlp = NLPProcessor()
        print("✓ NLP Processor initialized")
        
        # Test NLP functionality
        result = nlp.process_natural_language("create a python function")
        print(f"✓ NLP processing works: {result.intent}")
        
    except Exception as e:
        print(f"⚠ NLP Processor failed: {e}")
    
    try:
        from core.code_analyzer import CodeAnalyzer
        analyzer = CodeAnalyzer()
        print("✓ Code Analyzer initialized")
        
    except Exception as e:
        print(f"⚠ Code Analyzer failed: {e}")
    
    try:
        from core.autonomous_debugger import AutonomousDebugger
        debugger = AutonomousDebugger()
        print("✓ Autonomous Debugger initialized")
        
    except Exception as e:
        print(f"⚠ Autonomous Debugger failed: {e}")
    
    try:
        from core.web_search_rag import WebSearchRAG
        web_search = WebSearchRAG()
        print("✓ Web Search RAG initialized")
        
    except Exception as e:
        print(f"⚠ Web Search RAG failed: {e}")
    
    try:
        from core.task_manager import MultiThreadedTaskManager
        task_manager = MultiThreadedTaskManager(max_workers=2)
        print("✓ Task Manager initialized")
        task_manager.shutdown()
        
    except Exception as e:
        print(f"⚠ Task Manager failed: {e}")

def test_file_operations():
    """Test basic file operations."""
    print("\nTesting file operations...")
    
    # Create a test file
    test_file = Path("test_temp.py")
    test_content = '''def hello_world():
    """A simple test function."""
    print("Hello, World!")
    return "success"

if __name__ == "__main__":
    hello_world()
'''
    
    try:
        with open(test_file, 'w') as f:
            f.write(test_content)
        print("✓ File creation successful")
        
        # Test code analysis if available
        try:
            from core.code_analyzer import CodeAnalyzer
            analyzer = CodeAnalyzer()
            result = analyzer.analyze_file(str(test_file))
            print(f"✓ Code analysis successful: found {len(result.get('functions', []))} functions")
        except Exception as e:
            print(f"⚠ Code analysis failed: {e}")
        
        # Clean up
        test_file.unlink()
        print("✓ File cleanup successful")
        
    except Exception as e:
        print(f"✗ File operations failed: {e}")

def test_environment():
    """Test environment configuration."""
    print("\nTesting environment...")
    
    # Check for .env file
    if Path(".env").exists():
        print("✓ .env file found")
    else:
        print("⚠ .env file not found (optional)")
    
    # Check for required directories
    if Path("core").exists():
        print("✓ Core directory found")
    else:
        print("✗ Core directory missing")
        return False
    
    # Check Python version
    if sys.version_info >= (3, 8):
        print(f"✓ Python version {sys.version_info.major}.{sys.version_info.minor} is supported")
    else:
        print(f"⚠ Python version {sys.version_info.major}.{sys.version_info.minor} may not be fully supported")
    
    return True

def main():
    """Run all tests."""
    print("🤖 CODY Agent Test Suite")
    print("=" * 50)
    
    success = True
    
    # Run tests
    success &= test_imports()
    success &= test_environment()
    
    if success:
        test_core_modules()
        test_file_operations()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All critical tests passed! CODY is ready to use.")
        print("\nTo start CODY, run: python agent.py")
    else:
        print("❌ Some critical tests failed. Please check the installation.")
        print("\nTo install dependencies, run: pip install -r requirements.txt")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
