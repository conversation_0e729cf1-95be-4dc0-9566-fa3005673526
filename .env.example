# CODY Agent Configuration
# Copy this file to .env and fill in your API keys

# DeepSeek API Configuration (Required)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Gemini AI Configuration (Optional - for enhanced NLP)
GEMINI_API_KEY=your_gemini_api_key_here

# Web Search Configuration (Optional)
SERPAPI_KEY=your_serpapi_key_here

# Performance Configuration
MAX_CONCURRENT_TASKS=4
CACHE_SIZE_MB=100
ENABLE_PREDICTIVE_PREFETCHING=true
ENABLE_AUTONOMOUS_DEBUGGING=true

# Security Configuration
REQUIRE_POWERSHELL_CONFIRMATION=true
REQUIRE_BASH_CONFIRMATION=true
SAFE_MODE=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=cody_agent.log

# Feature Toggles
ENABLE_WEB_SEARCH=true
ENABLE_CODE_ANALYSIS=true
ENABLE_MULTI_THREADING=true
ENABLE_CONTEXT_COMPRESSION=true
