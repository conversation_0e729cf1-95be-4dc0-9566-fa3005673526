2025-06-14 16:43:49,612 - CODY - INFO - Advanced modules initialized successfully
2025-06-14 16:54:23,621 - CODY - INFO - Advanced modules initialized successfully
2025-06-14 16:54:33,635 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:54:43,645 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:54:53,660 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:55:03,669 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:55:13,675 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:55:23,680 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:55:33,688 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:55:43,694 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:55:53,701 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:56:03,712 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:56:13,715 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:56:23,728 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:56:33,735 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:56:43,736 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:56:53,737 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:57:03,750 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:57:13,754 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:57:23,760 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:57:33,775 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:57:43,786 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:57:53,794 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:59:36,473 - CODY - INFO - Advanced modules initialized successfully
2025-06-14 16:59:46,502 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 16:59:56,538 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:00:06,545 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:00:16,557 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:00:19,578 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-14 17:00:26,567 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:00:36,570 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:00:46,573 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:00:56,576 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:01:06,580 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:01:16,587 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:01:26,601 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:01:36,613 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:01:46,618 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:01:56,623 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:02:05,271 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-14 17:02:06,639 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:02:11,106 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-14 17:02:16,646 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:02:26,658 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:02:35,131 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-14 17:02:36,670 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:02:46,675 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:02:56,687 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:03:06,700 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:03:16,708 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:03:26,720 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:03:36,725 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:03:46,729 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:03:56,734 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:04:06,746 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:04:16,750 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:04:26,765 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:04:36,786 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:04:46,801 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:04:56,815 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:05:06,829 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:05:16,834 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:05:26,914 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:05:36,926 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:05:46,935 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:05:56,946 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:06:06,963 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:06:16,979 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:06:26,994 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:06:37,005 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:06:47,015 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:06:57,031 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:07:07,044 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:07:17,051 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:07:27,066 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:07:37,070 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:07:47,081 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:07:57,096 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:08:03,325 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-14 17:08:07,102 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:08:11,491 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-14 17:08:17,113 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:08:27,123 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:08:37,126 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:08:38,924 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-14 17:08:47,216 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:08:47,997 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-14 17:08:57,329 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:09:07,334 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:09:17,340 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:09:27,348 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:09:37,359 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:09:47,375 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:09:57,385 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:10:07,394 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:10:17,408 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:10:27,415 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:18:28,719 - CODY.CodebaseAwareness - INFO - Starting full scan of D:\Sandeep\AutoNomousSystems\CODY
2025-06-14 17:18:28,719 - CODY.CodebaseAwareness - INFO - Starting full scan of D:\Sandeep\AutoNomousSystems\CODY
2025-06-14 17:18:28,855 - CODY.CodebaseAwareness - INFO - Full scan completed in 0.13s. Indexed 20 files
2025-06-14 17:18:29,102 - CODY.CodebaseAwareness - INFO - Full scan completed in 0.38s. Indexed 20 files
2025-06-14 17:18:29,730 - CODY.PerformanceCore - WARNING - High memory usage: 92.6%
2025-06-14 17:18:29,833 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:31,863 - CODY.PerformanceCore - WARNING - High memory usage: 91.6%
2025-06-14 17:18:31,976 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:34,001 - CODY.PerformanceCore - WARNING - High memory usage: 91.5%
2025-06-14 17:18:34,136 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:36,161 - CODY.PerformanceCore - WARNING - High memory usage: 91.6%
2025-06-14 17:18:36,276 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:38,308 - CODY.PerformanceCore - WARNING - High memory usage: 91.7%
2025-06-14 17:18:38,422 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:38,719 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:18:40,449 - CODY.PerformanceCore - WARNING - High memory usage: 91.8%
2025-06-14 17:18:40,581 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:42,605 - CODY.PerformanceCore - WARNING - High memory usage: 92.0%
2025-06-14 17:18:42,712 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:44,745 - CODY.PerformanceCore - WARNING - High memory usage: 92.5%
2025-06-14 17:18:44,851 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:46,876 - CODY.PerformanceCore - WARNING - High memory usage: 92.5%
2025-06-14 17:18:46,989 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:48,722 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:18:49,030 - CODY.PerformanceCore - WARNING - High memory usage: 92.8%
2025-06-14 17:18:49,245 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:51,301 - CODY.PerformanceCore - WARNING - High memory usage: 94.3%
2025-06-14 17:18:51,449 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:53,482 - CODY.PerformanceCore - WARNING - High memory usage: 93.1%
2025-06-14 17:18:53,597 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:55,634 - CODY.PerformanceCore - WARNING - High memory usage: 93.4%
2025-06-14 17:18:55,756 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:57,791 - CODY.PerformanceCore - WARNING - High memory usage: 91.6%
2025-06-14 17:18:57,899 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:18:58,733 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:18:59,263 - CODY.CodebaseAwareness - INFO - Starting full scan of D:\Sandeep\AutoNomousSystems\CODY
2025-06-14 17:18:59,376 - CODY.CodebaseAwareness - INFO - Full scan completed in 0.11s. Indexed 20 files
2025-06-14 17:18:59,923 - CODY.PerformanceCore - WARNING - High memory usage: 91.6%
2025-06-14 17:19:00,028 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:02,090 - CODY.PerformanceCore - WARNING - High memory usage: 91.5%
2025-06-14 17:19:02,218 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:04,254 - CODY.PerformanceCore - WARNING - High memory usage: 89.6%
2025-06-14 17:19:04,359 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:06,392 - CODY.PerformanceCore - WARNING - High memory usage: 89.0%
2025-06-14 17:19:06,503 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:08,537 - CODY.PerformanceCore - WARNING - High memory usage: 88.8%
2025-06-14 17:19:08,641 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:08,736 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:19:10,678 - CODY.PerformanceCore - WARNING - High memory usage: 89.3%
2025-06-14 17:19:10,779 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:12,812 - CODY.PerformanceCore - WARNING - High memory usage: 89.0%
2025-06-14 17:19:12,912 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:14,944 - CODY.PerformanceCore - WARNING - High memory usage: 89.4%
2025-06-14 17:19:15,054 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:17,091 - CODY.PerformanceCore - WARNING - High memory usage: 89.5%
2025-06-14 17:19:17,192 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:18,747 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:19:19,221 - CODY.PerformanceCore - WARNING - High memory usage: 89.4%
2025-06-14 17:19:19,321 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:21,361 - CODY.PerformanceCore - WARNING - High memory usage: 89.9%
2025-06-14 17:19:21,469 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:23,489 - CODY.PerformanceCore - WARNING - High memory usage: 89.9%
2025-06-14 17:19:23,596 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:25,615 - CODY.PerformanceCore - WARNING - High memory usage: 90.0%
2025-06-14 17:19:25,729 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:27,778 - CODY.PerformanceCore - WARNING - High memory usage: 89.9%
2025-06-14 17:19:27,881 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:28,764 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:19:29,432 - CODY.CodebaseAwareness - INFO - Starting full scan of D:\Sandeep\AutoNomousSystems\CODY
2025-06-14 17:19:29,564 - CODY.CodebaseAwareness - INFO - Full scan completed in 0.13s. Indexed 20 files
2025-06-14 17:19:29,909 - CODY.PerformanceCore - WARNING - High memory usage: 90.2%
2025-06-14 17:19:30,014 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:32,062 - CODY.PerformanceCore - WARNING - High memory usage: 91.8%
2025-06-14 17:19:32,327 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:34,359 - CODY.PerformanceCore - WARNING - High memory usage: 92.1%
2025-06-14 17:19:34,462 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:36,489 - CODY.PerformanceCore - WARNING - High memory usage: 91.9%
2025-06-14 17:19:36,662 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:38,699 - CODY.PerformanceCore - WARNING - High memory usage: 93.4%
2025-06-14 17:19:38,838 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:19:38,839 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:40,876 - CODY.PerformanceCore - WARNING - High memory usage: 91.4%
2025-06-14 17:19:40,989 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:43,008 - CODY.PerformanceCore - WARNING - High memory usage: 90.5%
2025-06-14 17:19:43,147 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:45,215 - CODY.PerformanceCore - WARNING - High memory usage: 91.1%
2025-06-14 17:19:45,392 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:47,426 - CODY.PerformanceCore - WARNING - High memory usage: 92.3%
2025-06-14 17:19:47,543 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:48,849 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:19:49,574 - CODY.PerformanceCore - WARNING - High memory usage: 92.7%
2025-06-14 17:19:49,685 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:51,716 - CODY.PerformanceCore - WARNING - High memory usage: 93.0%
2025-06-14 17:19:51,834 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:53,870 - CODY.PerformanceCore - WARNING - High memory usage: 92.6%
2025-06-14 17:19:54,057 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:56,080 - CODY.PerformanceCore - WARNING - High memory usage: 91.4%
2025-06-14 17:19:56,238 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:58,265 - CODY.PerformanceCore - WARNING - High memory usage: 92.1%
2025-06-14 17:19:58,444 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:19:58,851 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:19:59,708 - CODY.CodebaseAwareness - INFO - Starting full scan of D:\Sandeep\AutoNomousSystems\CODY
2025-06-14 17:19:59,850 - CODY.CodebaseAwareness - INFO - Full scan completed in 0.14s. Indexed 20 files
2025-06-14 17:20:00,491 - CODY.PerformanceCore - WARNING - High memory usage: 93.0%
2025-06-14 17:20:00,640 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:02,664 - CODY.PerformanceCore - WARNING - High memory usage: 92.6%
2025-06-14 17:20:02,812 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:04,833 - CODY.PerformanceCore - WARNING - High memory usage: 93.1%
2025-06-14 17:20:04,960 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:06,988 - CODY.PerformanceCore - WARNING - High memory usage: 92.9%
2025-06-14 17:20:07,120 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:08,857 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:20:09,144 - CODY.PerformanceCore - WARNING - High memory usage: 93.0%
2025-06-14 17:20:09,257 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:11,305 - CODY.PerformanceCore - WARNING - High memory usage: 92.9%
2025-06-14 17:20:11,427 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:13,458 - CODY.PerformanceCore - WARNING - High memory usage: 93.4%
2025-06-14 17:20:13,585 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:15,615 - CODY.PerformanceCore - WARNING - High memory usage: 90.4%
2025-06-14 17:20:15,955 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:18,013 - CODY.PerformanceCore - WARNING - High memory usage: 85.7%
2025-06-14 17:20:18,245 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:18,870 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:20:20,277 - CODY.PerformanceCore - WARNING - High memory usage: 89.1%
2025-06-14 17:20:20,468 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:22,705 - CODY.PerformanceCore - WARNING - High memory usage: 89.9%
2025-06-14 17:20:22,896 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:24,923 - CODY.PerformanceCore - WARNING - High memory usage: 91.4%
2025-06-14 17:20:25,071 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:27,098 - CODY.PerformanceCore - WARNING - High memory usage: 90.9%
2025-06-14 17:20:27,223 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:28,885 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:20:29,248 - CODY.PerformanceCore - WARNING - High memory usage: 91.3%
2025-06-14 17:20:29,369 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:30,073 - CODY.CodebaseAwareness - INFO - Starting full scan of D:\Sandeep\AutoNomousSystems\CODY
2025-06-14 17:20:30,194 - CODY.CodebaseAwareness - INFO - Full scan completed in 0.12s. Indexed 20 files
2025-06-14 17:20:31,390 - CODY.PerformanceCore - WARNING - High memory usage: 91.6%
2025-06-14 17:20:31,575 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:33,601 - CODY.PerformanceCore - WARNING - High memory usage: 92.5%
2025-06-14 17:20:33,747 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:35,780 - CODY.PerformanceCore - WARNING - High memory usage: 92.7%
2025-06-14 17:20:35,923 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:37,963 - CODY.PerformanceCore - WARNING - High memory usage: 92.8%
2025-06-14 17:20:38,101 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:38,887 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:20:40,124 - CODY.PerformanceCore - WARNING - High memory usage: 93.8%
2025-06-14 17:20:40,398 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:42,431 - CODY.PerformanceCore - WARNING - High memory usage: 92.9%
2025-06-14 17:20:42,553 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:44,584 - CODY.PerformanceCore - WARNING - High memory usage: 93.2%
2025-06-14 17:20:44,804 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:46,840 - CODY.PerformanceCore - WARNING - High memory usage: 92.4%
2025-06-14 17:20:46,957 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:48,891 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:20:48,995 - CODY.PerformanceCore - WARNING - High memory usage: 92.0%
2025-06-14 17:20:49,114 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:51,148 - CODY.PerformanceCore - WARNING - High memory usage: 93.6%
2025-06-14 17:20:51,285 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:53,309 - CODY.PerformanceCore - WARNING - High memory usage: 93.1%
2025-06-14 17:20:53,433 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:55,454 - CODY.PerformanceCore - WARNING - High memory usage: 93.3%
2025-06-14 17:20:55,586 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:57,613 - CODY.PerformanceCore - WARNING - High memory usage: 93.1%
2025-06-14 17:20:57,741 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:20:58,897 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:20:59,755 - CODY.PerformanceCore - WARNING - High memory usage: 93.1%
2025-06-14 17:20:59,998 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:00,432 - CODY.CodebaseAwareness - INFO - Starting full scan of D:\Sandeep\AutoNomousSystems\CODY
2025-06-14 17:21:00,592 - CODY.CodebaseAwareness - INFO - Full scan completed in 0.16s. Indexed 20 files
2025-06-14 17:21:02,044 - CODY.PerformanceCore - WARNING - High memory usage: 93.0%
2025-06-14 17:21:02,198 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:04,230 - CODY.PerformanceCore - WARNING - High memory usage: 92.2%
2025-06-14 17:21:04,381 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:06,423 - CODY.PerformanceCore - WARNING - High memory usage: 92.7%
2025-06-14 17:21:06,547 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:08,585 - CODY.PerformanceCore - WARNING - High memory usage: 92.6%
2025-06-14 17:21:08,706 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:08,905 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:21:10,734 - CODY.PerformanceCore - WARNING - High memory usage: 92.5%
2025-06-14 17:21:10,858 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:12,882 - CODY.PerformanceCore - WARNING - High memory usage: 93.2%
2025-06-14 17:21:13,014 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:15,127 - CODY.PerformanceCore - WARNING - High memory usage: 91.5%
2025-06-14 17:21:15,553 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:17,587 - CODY.PerformanceCore - WARNING - High memory usage: 92.0%
2025-06-14 17:21:17,747 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:18,919 - CODY.TaskManager - INFO - Performance metrics: {'tasks_executed': 0, 'tasks_failed': 0, 'average_execution_time': 0.0, 'cache_hits': 0, 'cache_misses': 0}
2025-06-14 17:21:19,771 - CODY.PerformanceCore - WARNING - High memory usage: 92.6%
2025-06-14 17:21:19,921 - CODY.PerformanceCore - INFO - Triggered memory optimization
2025-06-14 17:21:21,971 - CODY.PerformanceCore - WARNING - High memory usage: 93.3%
2025-06-14 17:21:22,169 - CODY.PerformanceCore - INFO - Triggered memory optimization
