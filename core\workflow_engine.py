#!/usr/bin/env python3

"""
Enhanced Iterative Workflow Engine for CODY Agent
Implements single-step execution with execute→analyze→plan→execute cycle
Following the exact workflow diagram in workflow.md
"""

import asyncio
import time
import os
import re
import subprocess
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import deque
import json
from pathlib import Path

logger = logging.getLogger('CODY.WorkflowEngine')

class TaskType(Enum):
    """Types of tasks in the workflow."""
    FILE_OPERATION = "file_operation"
    TERMINAL_EXECUTION = "terminal_execution"
    CODE_GENERATION = "code_generation"
    BUG_DETECTION = "bug_detection"
    WEB_SEARCH = "web_search"
    CODE_TRANSLATION = "code_translation"
    ANALYSIS = "analysis"
    REFACTORING = "refactoring"
    TESTING = "testing"

class WorkflowStage(Enum):
    """Stages in the workflow following workflow.md diagram."""
    USER_INPUT = "user_input"
    NLP_UNDERSTANDING = "nlp_understanding"
    CONTEXT_ENGINE = "context_engine"
    INTENT_ANALYSIS = "intent_analysis"
    TASK_ROUTING = "task_routing"
    TASK_EXECUTION = "task_execution"
    SELF_EVALUATION = "self_evaluation"
    SMART_SUGGESTIONS = "smart_suggestions"
    RESULT_LOGGING = "result_logging"
    OUTPUT_GENERATION = "output_generation"

class ExecutionPhase(Enum):
    """Phases in the iterative execution cycle."""
    EXECUTE = "execute"
    ANALYZE = "analyze"
    PLAN = "plan"
    VALIDATE = "validate"

class OperationStatus(Enum):
    """Status of individual operations."""
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    ANALYZING = "analyzing"
    PLANNING = "planning"

@dataclass
class ExecutionResult:
    """Result of a single execution step."""
    operation_id: str
    operation_type: str
    status: OperationStatus
    output: str = ""
    error_message: str = ""
    exit_code: int = 0
    execution_time: float = 0.0
    files_modified: List[str] = field(default_factory=list)
    files_created: List[str] = field(default_factory=list)
    files_deleted: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)

@dataclass
class AnalysisResult:
    """Result of analyzing an execution step."""
    success: bool
    insights: List[str] = field(default_factory=list)
    errors_found: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    performance_issues: List[str] = field(default_factory=list)
    file_system_changes: Dict[str, Any] = field(default_factory=dict)
    next_action_recommendations: List[str] = field(default_factory=list)
    confidence_score: float = 0.0
    analysis_time: float = 0.0

@dataclass
class PlanStep:
    """A planned step in the execution sequence."""
    step_id: str
    operation_type: str
    description: str
    command: str = ""
    file_path: str = ""
    content: str = ""
    dependencies: List[str] = field(default_factory=list)
    risk_level: str = "low"  # low, medium, high
    estimated_time: float = 1.0
    validation_criteria: List[str] = field(default_factory=list)

@dataclass
class WorkflowStep:
    """Enhanced workflow step with iterative capabilities."""
    step_id: str
    stage: WorkflowStage
    phase: ExecutionPhase
    task_type: Optional[TaskType] = None
    input_data: Dict[str, Any] = field(default_factory=dict)
    execution_result: Optional[ExecutionResult] = None
    analysis_result: Optional[AnalysisResult] = None
    planned_steps: List[PlanStep] = field(default_factory=list)
    status: OperationStatus = OperationStatus.PENDING
    start_time: float = 0.0
    end_time: float = 0.0
    chain_of_thought: List[str] = field(default_factory=list)
    iteration_count: int = 0

@dataclass
class WorkflowContext:
    """Complete context for the workflow."""
    user_input: str
    chat_history: List[Dict[str, Any]] = field(default_factory=list)
    open_files: List[str] = field(default_factory=list)
    terminal_output: List[str] = field(default_factory=list)
    commands_history: List[str] = field(default_factory=list)
    current_directory: str = "."
    project_structure: Dict[str, Any] = field(default_factory=dict)
    active_files: Dict[str, str] = field(default_factory=dict)  # filename -> content
    file_changes: List[Dict[str, Any]] = field(default_factory=list)
    memory_cache: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)

class ChainOfThoughtReasoner:
    """Implements Chain-of-Thought reasoning for complex problem solving."""
    
    def __init__(self):
        self.reasoning_steps = []
        
    def think_step_by_step(self, problem: str, context: WorkflowContext) -> List[str]:
        """Break down complex problems into logical steps."""
        reasoning_steps = []
        
        # Step 1: Understand the problem
        reasoning_steps.append(f"🤔 Understanding: {problem}")
        
        # Step 2: Analyze context
        if context.open_files:
            reasoning_steps.append(f"📁 Context: Working with {len(context.open_files)} files")
        
        if context.terminal_output:
            reasoning_steps.append(f"💻 Terminal: Previous commands executed")
        
        # Step 3: Break down the task
        if "create" in problem.lower():
            reasoning_steps.append("🔨 Task: File/code creation required")
        elif "fix" in problem.lower() or "debug" in problem.lower():
            reasoning_steps.append("🐛 Task: Debugging/fixing required")
        elif "search" in problem.lower() or "find" in problem.lower():
            reasoning_steps.append("🔍 Task: Search operation required")
        elif "install" in problem.lower() or "setup" in problem.lower():
            reasoning_steps.append("⚙️ Task: Installation/setup required")
        
        # Step 4: Plan execution
        reasoning_steps.append("📋 Planning: Determining optimal execution strategy")
        
        # Step 5: Consider dependencies
        reasoning_steps.append("🔗 Dependencies: Checking for prerequisites")
        
        return reasoning_steps
    
    def self_critique(self, solution: str, expected_outcome: str) -> Tuple[bool, List[str]]:
        """Evaluate and critique the generated solution."""
        critique_points = []
        is_satisfactory = True
        
        # Check completeness
        if len(solution.strip()) < 10:
            critique_points.append("❌ Solution seems too brief")
            is_satisfactory = False
        
        # Check for common issues
        if "TODO" in solution or "FIXME" in solution:
            critique_points.append("⚠️ Solution contains unfinished parts")
            is_satisfactory = False
        
        # Check for best practices
        if "def " in solution and "\"\"\"" not in solution:
            critique_points.append("📝 Consider adding docstrings to functions")
        
        if is_satisfactory:
            critique_points.append("✅ Solution looks good and complete")
        
        return is_satisfactory, critique_points

class SmartContextRouter:
    """Routes tasks based on intelligent context analysis."""
    
    def __init__(self):
        self.file_patterns = {
            'create': ['create', 'make', 'generate', 'build', 'new'],
            'edit': ['edit', 'modify', 'change', 'update', 'fix'],
            'delete': ['delete', 'remove', 'rm'],
            'read': ['read', 'show', 'display', 'view', 'cat'],
            'search': ['search', 'find', 'grep', 'look for']
        }
        
        self.terminal_patterns = {
            'install': ['install', 'npm install', 'pip install', 'apt install'],
            'run': ['run', 'execute', 'start', 'launch'],
            'git': ['git', 'commit', 'push', 'pull', 'clone'],
            'test': ['test', 'pytest', 'npm test', 'jest']
        }
    
    def route_task(self, user_input: str, context: WorkflowContext) -> TaskType:
        """Determine the appropriate task type based on input and context."""
        input_lower = user_input.lower()
        
        # Check for file operations
        for operation, patterns in self.file_patterns.items():
            if any(pattern in input_lower for pattern in patterns):
                if any(ext in input_lower for ext in ['.py', '.js', '.html', '.css', '.java', '.cpp']):
                    return TaskType.FILE_OPERATION
        
        # Check for terminal operations
        for operation, patterns in self.terminal_patterns.items():
            if any(pattern in input_lower for pattern in patterns):
                return TaskType.TERMINAL_EXECUTION
        
        # Check for debugging
        if any(word in input_lower for word in ['debug', 'fix', 'error', 'bug']):
            return TaskType.BUG_DETECTION
        
        # Check for web search
        if any(word in input_lower for word in ['search', 'find', 'documentation', 'example']):
            return TaskType.WEB_SEARCH
        
        # Check for code translation
        if any(phrase in input_lower for phrase in ['convert', 'translate', 'port to']):
            return TaskType.CODE_TRANSLATION
        
        # Default to code generation
        return TaskType.CODE_GENERATION

class FileTracker:
    """Monitors and tracks file changes in the project."""
    
    def __init__(self):
        self.tracked_files = {}
        self.change_history = []
    
    def track_file(self, filepath: str, content: str) -> None:
        """Start tracking a file."""
        self.tracked_files[filepath] = {
            'content': content,
            'last_modified': time.time(),
            'change_count': 0
        }
    
    def update_file(self, filepath: str, new_content: str) -> Dict[str, Any]:
        """Update file content and track changes."""
        if filepath in self.tracked_files:
            old_content = self.tracked_files[filepath]['content']
            change_info = {
                'filepath': filepath,
                'timestamp': time.time(),
                'old_size': len(old_content),
                'new_size': len(new_content),
                'lines_added': new_content.count('\n') - old_content.count('\n')
            }
            
            self.tracked_files[filepath]['content'] = new_content
            self.tracked_files[filepath]['last_modified'] = time.time()
            self.tracked_files[filepath]['change_count'] += 1
            
            self.change_history.append(change_info)
            return change_info
        
        return {}
    
    def get_active_files(self) -> List[str]:
        """Get list of currently active/tracked files."""
        return list(self.tracked_files.keys())

class EnhancedWorkflowEngine:
    """Enhanced workflow engine with iterative execution capabilities."""

    def __init__(self, nlp_processor, code_analyzer, debugger, web_search, task_manager):
        self.nlp_processor = nlp_processor
        self.code_analyzer = code_analyzer
        self.debugger = debugger
        self.web_search = web_search
        self.task_manager = task_manager

        self.reasoner = ChainOfThoughtReasoner()
        self.router = SmartContextRouter()
        self.file_tracker = FileTracker()

        # Enhanced components
        from .enhanced_file_operations import EnhancedFileOperations
        from .iterative_execution_engine import IterativeExecutionEngine

        self.file_ops = EnhancedFileOperations()
        self.iterative_engine = IterativeExecutionEngine(self.file_ops)

        self.workflow_history = []
        self.current_context = WorkflowContext(user_input="")

        # Performance tracking
        self.execution_metrics = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'average_execution_time': 0.0,
            'total_execution_time': 0.0
        }
    
    async def process_user_input(self, user_input: str) -> Dict[str, Any]:
        """Process user input through the enhanced iterative workflow."""
        workflow_id = f"workflow_{int(time.time())}"
        start_time = time.time()

        # Initialize context
        self.current_context.user_input = user_input
        self.current_context.current_directory = os.getcwd()
        self.current_context.active_files = self.file_ops.get_active_files()

        try:
            # Execute iterative workflow following the diagram
            result = await self._execute_enhanced_workflow(workflow_id, user_input)

            # Update metrics
            execution_time = time.time() - start_time
            self._update_metrics(result.get('goal_achieved', False), execution_time)

            # Store in history
            self.workflow_history.append({
                'workflow_id': workflow_id,
                'user_input': user_input,
                'result': result,
                'timestamp': time.time(),
                'execution_time': execution_time
            })

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Enhanced workflow failed: {e}")

            # Update metrics for failure
            self._update_metrics(False, execution_time)

            return {
                'workflow_id': workflow_id,
                'success': False,
                'error': str(e),
                'execution_time': execution_time,
                'fallback_used': True
            }

    async def _execute_enhanced_workflow(self, workflow_id: str, user_input: str) -> Dict[str, Any]:
        """Execute the enhanced workflow following the exact diagram structure."""
        workflow_start = time.time()

        # Stage 1: Natural Language Understanding
        nlp_result = await self._stage_nlp_understanding(user_input)

        # Stage 2: Context Engine (Chat History, Open Files, Terminal Output, Commands)
        context_result = await self._stage_context_engine_enhanced()

        # Stage 3: Smart Context Router & Intent Analyzer
        intent_result = await self._stage_intent_analysis_enhanced(user_input, context_result)

        # Stage 4: Task Routing (File-related vs Other)
        routing_result = await self._stage_task_routing_enhanced(intent_result)

        # Stage 5: Iterative Execution (Execute → Analyze → Plan → Execute)
        execution_result = await self._stage_iterative_execution(routing_result)

        # Stage 6: Self-Evaluation + Code Improver (Chain-of-Thought + RAG)
        evaluation_result = await self._stage_self_evaluation_enhanced(execution_result)

        # Stage 7: Smart Suggestions Engine
        suggestions_result = await self._stage_smart_suggestions_enhanced(evaluation_result)

        # Stage 8: Result Logger
        logging_result = await self._stage_result_logging_enhanced(workflow_id, {
            'nlp': nlp_result,
            'context': context_result,
            'intent': intent_result,
            'routing': routing_result,
            'execution': execution_result,
            'evaluation': evaluation_result,
            'suggestions': suggestions_result
        })

        # Stage 9: Output to User (via interactive CLI)
        output_result = await self._stage_output_generation_enhanced(logging_result)

        total_time = time.time() - workflow_start

        return {
            'workflow_id': workflow_id,
            'success': True,
            'stages': {
                'nlp_understanding': nlp_result,
                'context_engine': context_result,
                'intent_analysis': intent_result,
                'task_routing': routing_result,
                'iterative_execution': execution_result,
                'self_evaluation': evaluation_result,
                'smart_suggestions': suggestions_result,
                'result_logging': logging_result,
                'output_generation': output_result
            },
            'goal_achieved': execution_result.get('goal_achieved', False),
            'total_execution_time': total_time,
            'operations_completed': execution_result.get('operations_completed', 0),
            'iterations': execution_result.get('iterations', [])
        }
    
    async def _execute_workflow_stages(self, workflow_id: str, user_input: str) -> Dict[str, Any]:
        """Execute all workflow stages in sequence."""
        stages_result = {}
        
        try:
            # Stage 1: Natural Language Understanding
            nlp_result = await self._stage_nlp_understanding(user_input)
            stages_result['nlp'] = nlp_result
            
            # Stage 2: Context Engine
            context_result = await self._stage_context_engine(nlp_result)
            stages_result['context'] = context_result
            
            # Stage 3: Intent Analysis & Task Routing
            intent_result = await self._stage_intent_analysis(user_input, context_result)
            stages_result['intent'] = intent_result
            
            # Stage 4: Task Execution
            execution_result = await self._stage_task_execution(intent_result)
            stages_result['execution'] = execution_result
            
            # Stage 5: Self-Evaluation
            eval_result = await self._stage_self_evaluation(execution_result)
            stages_result['evaluation'] = eval_result
            
            # Stage 6: Smart Suggestions
            suggestions_result = await self._stage_smart_suggestions(eval_result)
            stages_result['suggestions'] = suggestions_result
            
            # Stage 7: Result Logging
            logging_result = await self._stage_result_logging(stages_result)
            stages_result['logging'] = logging_result
            
            # Stage 8: Output Generation
            output_result = await self._stage_output_generation(stages_result)
            stages_result['output'] = output_result
            
            return {
                'success': True,
                'workflow_id': workflow_id,
                'stages': stages_result,
                'final_output': output_result
            }
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'stages': stages_result
            }
    
    async def _stage_nlp_understanding(self, user_input: str) -> Dict[str, Any]:
        """Stage 1: Natural Language Understanding."""
        if self.nlp_processor:
            intent_result = self.nlp_processor.process_natural_language(user_input)
            return {
                'intent': intent_result.intent.value,
                'confidence': intent_result.confidence,
                'entities': intent_result.entities,
                'processed_text': intent_result.processed_text
            }
        return {'intent': 'unknown', 'confidence': 0.0}
    
    async def _stage_context_engine(self, nlp_result: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 2: Context Engine - gather all relevant context."""
        return {
            'chat_history_size': len(self.current_context.chat_history),
            'open_files': self.file_tracker.get_active_files(),
            'current_directory': self.current_context.current_directory,
            'recent_changes': len(self.current_context.file_changes),
            'memory_cache_size': len(self.current_context.memory_cache)
        }
    
    async def _stage_intent_analysis(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 3: Intent Analysis and Task Routing."""
        # Chain-of-Thought reasoning
        reasoning_steps = self.reasoner.think_step_by_step(user_input, self.current_context)
        
        # Route task
        task_type = self.router.route_task(user_input, self.current_context)
        
        return {
            'task_type': task_type.value,
            'reasoning_steps': reasoning_steps,
            'is_file_related': task_type == TaskType.FILE_OPERATION,
            'requires_terminal': task_type == TaskType.TERMINAL_EXECUTION
        }
    
    async def _stage_task_execution(self, intent_result: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 4: Task Execution with multithreading."""
        task_type = TaskType(intent_result['task_type'])
        
        # Execute based on task type
        if task_type == TaskType.FILE_OPERATION:
            return await self._execute_file_operation()
        elif task_type == TaskType.TERMINAL_EXECUTION:
            return await self._execute_terminal_operation()
        elif task_type == TaskType.CODE_GENERATION:
            return await self._execute_code_generation()
        elif task_type == TaskType.BUG_DETECTION:
            return await self._execute_bug_detection()
        elif task_type == TaskType.WEB_SEARCH:
            return await self._execute_web_search()
        else:
            return {'status': 'not_implemented', 'task_type': task_type.value}
    
    async def _execute_file_operation(self) -> Dict[str, Any]:
        """Execute file-related operations."""
        return {'operation': 'file_operation', 'status': 'completed'}
    
    async def _execute_terminal_operation(self) -> Dict[str, Any]:
        """Execute terminal commands."""
        return {'operation': 'terminal_operation', 'status': 'completed'}
    
    async def _execute_code_generation(self) -> Dict[str, Any]:
        """Execute code generation."""
        return {'operation': 'code_generation', 'status': 'completed'}
    
    async def _execute_bug_detection(self) -> Dict[str, Any]:
        """Execute bug detection and fixing."""
        return {'operation': 'bug_detection', 'status': 'completed'}
    
    async def _execute_web_search(self) -> Dict[str, Any]:
        """Execute web search operations."""
        return {'operation': 'web_search', 'status': 'completed'}
    
    async def _stage_self_evaluation(self, execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 5: Self-Evaluation with Chain-of-Thought."""
        # Evaluate the execution result
        is_satisfactory, critique_points = self.reasoner.self_critique(
            str(execution_result), 
            self.current_context.user_input
        )
        
        return {
            'is_satisfactory': is_satisfactory,
            'critique_points': critique_points,
            'needs_retry': not is_satisfactory
        }
    
    async def _stage_smart_suggestions(self, eval_result: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 6: Smart Suggestions Engine."""
        suggestions = []
        
        if not eval_result['is_satisfactory']:
            suggestions.append("🔄 Consider retrying with more specific requirements")
        
        suggestions.extend([
            "📝 Add documentation for better maintainability",
            "🧪 Generate unit tests for the code",
            "🔍 Run static analysis for code quality"
        ])
        
        return {'suggestions': suggestions}
    
    async def _stage_result_logging(self, stages_result: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 7: Result Logging."""
        log_entry = {
            'timestamp': time.time(),
            'user_input': self.current_context.user_input,
            'stages_completed': list(stages_result.keys()),
            'success': stages_result.get('execution', {}).get('status') == 'completed'
        }
        
        logger.info(f"Workflow completed: {log_entry}")
        return log_entry
    
    async def _stage_context_engine_enhanced(self) -> Dict[str, Any]:
        """Enhanced Context Engine - comprehensive context gathering."""
        return {
            'chat_history_size': len(self.current_context.chat_history),
            'open_files': list(self.current_context.active_files.keys()),
            'active_file_count': len(self.current_context.active_files),
            'current_directory': self.current_context.current_directory,
            'recent_changes': len(self.current_context.file_changes),
            'memory_cache_size': len(self.current_context.memory_cache),
            'file_system_changes': self.file_ops.get_file_changes(),
            'project_structure': self._analyze_project_structure()
        }

    async def _stage_intent_analysis_enhanced(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced Intent Analysis with comprehensive reasoning."""
        # Chain-of-Thought reasoning
        reasoning_steps = self.reasoner.think_step_by_step(user_input, self.current_context)

        # Enhanced task routing
        task_type = self.router.route_task(user_input, self.current_context)

        # Analyze complexity
        complexity_score = self._analyze_request_complexity(user_input)

        return {
            'task_type': task_type.value,
            'reasoning_steps': reasoning_steps,
            'complexity_score': complexity_score,
            'is_file_related': self._is_file_related_request(user_input),
            'requires_terminal': self._requires_terminal_execution(user_input),
            'estimated_steps': self._estimate_required_steps(user_input),
            'risk_assessment': self._assess_risk_level(user_input)
        }

    async def _stage_task_routing_enhanced(self, intent_result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced Task Routing following the workflow diagram."""
        is_file_related = intent_result.get('is_file_related', False)

        if is_file_related:
            # Route to File Manager
            return {
                'route': 'file_manager',
                'handler': 'enhanced_file_operations',
                'operations': ['create', 'edit', 'delete', 'move', 'search'],
                'priority': 'high'
            }
        else:
            # Route to Task Handler Module (Multithreaded)
            task_type = intent_result.get('task_type', 'unknown')

            if 'terminal' in task_type or 'command' in task_type:
                handler = 'terminal_execution'
            elif 'code' in task_type or 'generate' in task_type:
                handler = 'code_generation'
            elif 'debug' in task_type or 'fix' in task_type:
                handler = 'bug_detection_fix'
            elif 'search' in task_type or 'web' in task_type:
                handler = 'web_search'
            else:
                handler = 'general_execution'

            return {
                'route': 'task_handler',
                'handler': handler,
                'multithreaded': True,
                'priority': intent_result.get('complexity_score', 1)
            }

    async def _stage_iterative_execution(self, routing_result: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 5: Iterative Execution Engine (Execute → Analyze → Plan → Execute)."""
        try:
            # Use the iterative execution engine
            execution_result = await self.iterative_engine.execute_iterative_workflow(
                self.current_context.user_input
            )

            return {
                'status': 'completed',
                'execution_result': execution_result,
                'goal_achieved': execution_result.get('goal_achieved', False),
                'operations_completed': execution_result.get('operations_completed', 0),
                'iterations': execution_result.get('iterations', []),
                'total_time': execution_result.get('total_time', 0.0),
                'final_status': execution_result.get('final_status', 'unknown')
            }

        except Exception as e:
            logger.error(f"Iterative execution failed: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'goal_achieved': False,
                'operations_completed': 0
            }

    async def _stage_self_evaluation_enhanced(self, execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced Self-Evaluation with Chain-of-Thought + RAG."""
        if execution_result.get('status') != 'completed':
            return {
                'evaluation': 'skipped',
                'reason': 'execution_failed'
            }

        exec_data = execution_result.get('execution_result', {})

        # Chain-of-Thought evaluation
        evaluation_thoughts = []
        evaluation_thoughts.append("🔍 Analyzing execution results...")

        goal_achieved = exec_data.get('goal_achieved', False)
        operations_completed = exec_data.get('operations_completed', 0)
        iterations = exec_data.get('iterations', [])

        if goal_achieved:
            evaluation_thoughts.append("✅ User requirements successfully fulfilled")
        else:
            evaluation_thoughts.append("⚠️ User requirements not fully met")

        evaluation_thoughts.append(f"📊 Completed {operations_completed} operations in {len(iterations)} iterations")

        # Performance evaluation
        total_time = exec_data.get('total_time', 0.0)
        if total_time > 10.0:
            evaluation_thoughts.append("⏱️ Execution time was longer than optimal")
        else:
            evaluation_thoughts.append("⚡ Execution completed efficiently")

        # Quality assessment
        quality_score = self._calculate_quality_score(exec_data)

        return {
            'evaluation': 'completed',
            'goal_achieved': goal_achieved,
            'quality_score': quality_score,
            'evaluation_thoughts': evaluation_thoughts,
            'performance_metrics': {
                'operations_completed': operations_completed,
                'iterations_used': len(iterations),
                'execution_time': total_time,
                'efficiency_rating': 'high' if total_time < 5.0 else 'medium' if total_time < 10.0 else 'low'
            },
            'improvement_suggestions': self._generate_improvement_suggestions(exec_data)
        }

    async def _stage_smart_suggestions_enhanced(self, evaluation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced Smart Suggestions Engine."""
        suggestions = []

        if evaluation_result.get('evaluation') == 'skipped':
            suggestions.append("🔄 Consider retrying the operation with different parameters")
            return {'suggestions': suggestions}

        goal_achieved = evaluation_result.get('goal_achieved', False)
        quality_score = evaluation_result.get('quality_score', 0.0)

        if goal_achieved:
            suggestions.extend([
                "📝 Consider adding documentation for the completed work",
                "🧪 Run tests to validate the implementation",
                "🔍 Review the code for potential optimizations",
                "📊 Monitor performance metrics for future improvements"
            ])
        else:
            suggestions.extend([
                "🔄 Analyze the remaining requirements and retry",
                "🔍 Check for any error messages or warnings",
                "💡 Consider breaking down the task into smaller steps",
                "🛠️ Review the approach and try alternative methods"
            ])

        # Quality-based suggestions
        if quality_score < 0.7:
            suggestions.append("⚡ Consider optimizing the execution approach")

        # Add improvement suggestions from evaluation
        improvement_suggestions = evaluation_result.get('improvement_suggestions', [])
        suggestions.extend(improvement_suggestions)

        return {
            'suggestions': suggestions[:5],  # Limit to top 5 suggestions
            'next_actions': self._suggest_next_actions(evaluation_result),
            'learning_opportunities': self._identify_learning_opportunities(evaluation_result)
        }

    async def _stage_result_logging_enhanced(self, workflow_id: str, stages_result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced Result Logging with comprehensive tracking."""
        log_entry = {
            'workflow_id': workflow_id,
            'timestamp': time.time(),
            'user_input': self.current_context.user_input,
            'stages_completed': list(stages_result.keys()),
            'success': stages_result.get('execution', {}).get('goal_achieved', False),
            'operations_completed': stages_result.get('execution', {}).get('operations_completed', 0),
            'total_execution_time': stages_result.get('execution', {}).get('total_time', 0.0),
            'quality_score': stages_result.get('evaluation', {}).get('quality_score', 0.0),
            'iterations_used': len(stages_result.get('execution', {}).get('iterations', [])),
            'files_affected': self._get_affected_files(stages_result),
            'performance_metrics': self._extract_performance_metrics(stages_result)
        }

        # Log to file for detailed tracking
        logger.info(f"Workflow completed: {log_entry}")

        return log_entry

    async def _stage_output_generation_enhanced(self, logging_result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced Output Generation for interactive CLI."""
        success = logging_result.get('success', False)
        operations_completed = logging_result.get('operations_completed', 0)
        execution_time = logging_result.get('total_execution_time', 0.0)
        quality_score = logging_result.get('quality_score', 0.0)

        if success:
            message = f"✅ Task completed successfully! ({operations_completed} operations in {execution_time:.2f}s)"
            status_emoji = "🎉"
        else:
            message = f"⚠️ Task partially completed. ({operations_completed} operations attempted)"
            status_emoji = "🔄"

        return {
            'message': message,
            'status_emoji': status_emoji,
            'success': success,
            'execution_summary': {
                'operations_completed': operations_completed,
                'execution_time': execution_time,
                'quality_score': quality_score,
                'iterations_used': logging_result.get('iterations_used', 0)
            },
            'files_affected': logging_result.get('files_affected', []),
            'next_suggestions': logging_result.get('suggestions', []),
            'detailed_results': logging_result
        }

    # Helper methods for enhanced workflow
    def _analyze_project_structure(self) -> Dict[str, Any]:
        """Analyze current project structure."""
        try:
            current_dir = Path(self.current_context.current_directory)

            structure = {
                'root_path': str(current_dir),
                'file_count': 0,
                'directory_count': 0,
                'languages_detected': [],
                'main_files': []
            }

            # Count files and directories
            for item in current_dir.rglob('*'):
                if item.is_file():
                    structure['file_count'] += 1

                    # Detect languages
                    suffix = item.suffix.lower()
                    if suffix == '.py' and 'python' not in structure['languages_detected']:
                        structure['languages_detected'].append('python')
                    elif suffix in ['.js', '.jsx', '.ts', '.tsx'] and 'javascript' not in structure['languages_detected']:
                        structure['languages_detected'].append('javascript')
                    elif suffix == '.java' and 'java' not in structure['languages_detected']:
                        structure['languages_detected'].append('java')

                    # Identify main files
                    if item.name in ['main.py', 'index.js', 'app.py', 'server.js', 'main.java']:
                        structure['main_files'].append(str(item))

                elif item.is_dir():
                    structure['directory_count'] += 1

            return structure

        except Exception as e:
            logger.debug(f"Project structure analysis failed: {e}")
            return {'error': str(e)}

    def _analyze_request_complexity(self, user_input: str) -> float:
        """Analyze the complexity of the user request."""
        complexity_indicators = {
            'length': len(user_input) / 100.0,  # Normalize by 100 chars
            'technical_terms': len(re.findall(r'\b(?:function|class|method|variable|API|database|server|deploy|test)\b', user_input.lower())) * 0.2,
            'file_operations': len(re.findall(r'\b(?:create|edit|delete|read|write|file|folder|directory)\b', user_input.lower())) * 0.3,
            'complexity_keywords': len(re.findall(r'\b(?:complex|advanced|sophisticated|comprehensive|full|complete)\b', user_input.lower())) * 0.4
        }

        total_score = sum(complexity_indicators.values())
        return min(total_score, 5.0)  # Cap at 5.0

    def _is_file_related_request(self, user_input: str) -> bool:
        """Check if the request is file-related."""
        file_keywords = ['file', 'folder', 'directory', 'create', 'edit', 'delete', 'read', 'write', 'save', 'open']
        return any(keyword in user_input.lower() for keyword in file_keywords)

    def _requires_terminal_execution(self, user_input: str) -> bool:
        """Check if the request requires terminal execution."""
        terminal_keywords = ['run', 'execute', 'command', 'terminal', 'shell', 'bash', 'cmd', 'install', 'npm', 'pip']
        return any(keyword in user_input.lower() for keyword in terminal_keywords)

    def _estimate_required_steps(self, user_input: str) -> int:
        """Estimate the number of steps required."""
        base_steps = 1

        # Add steps based on complexity indicators
        if 'and' in user_input.lower():
            base_steps += user_input.lower().count('and')

        if 'then' in user_input.lower():
            base_steps += user_input.lower().count('then')

        # Add steps for complex operations
        complex_operations = ['deploy', 'setup', 'configure', 'install', 'build', 'test']
        for op in complex_operations:
            if op in user_input.lower():
                base_steps += 2

        return min(base_steps, 10)  # Cap at 10 steps

    def _assess_risk_level(self, user_input: str) -> str:
        """Assess the risk level of the request."""
        high_risk_keywords = ['delete', 'remove', 'rm', 'format', 'destroy', 'drop', 'truncate']
        medium_risk_keywords = ['modify', 'change', 'update', 'edit', 'move', 'rename']

        if any(keyword in user_input.lower() for keyword in high_risk_keywords):
            return 'high'
        elif any(keyword in user_input.lower() for keyword in medium_risk_keywords):
            return 'medium'
        else:
            return 'low'

    def _calculate_quality_score(self, execution_data: Dict[str, Any]) -> float:
        """Calculate quality score based on execution results."""
        base_score = 1.0

        # Reduce score for failures
        if not execution_data.get('goal_achieved', False):
            base_score -= 0.5

        # Reduce score for excessive iterations
        iterations = len(execution_data.get('iterations', []))
        if iterations > 5:
            base_score -= 0.2

        # Reduce score for long execution time
        execution_time = execution_data.get('total_time', 0.0)
        if execution_time > 10.0:
            base_score -= 0.2

        # Reduce score for errors
        final_status = execution_data.get('final_status', '')
        if 'failed' in final_status:
            base_score -= 0.3

        return max(base_score, 0.0)

    def _generate_improvement_suggestions(self, execution_data: Dict[str, Any]) -> List[str]:
        """Generate improvement suggestions based on execution data."""
        suggestions = []

        iterations = len(execution_data.get('iterations', []))
        if iterations > 3:
            suggestions.append("🔄 Consider breaking down complex tasks into smaller steps")

        execution_time = execution_data.get('total_time', 0.0)
        if execution_time > 10.0:
            suggestions.append("⚡ Look for opportunities to optimize execution speed")

        if not execution_data.get('goal_achieved', False):
            suggestions.append("🎯 Review requirements and ensure all steps are covered")

        return suggestions

    def _suggest_next_actions(self, evaluation_result: Dict[str, Any]) -> List[str]:
        """Suggest next actions based on evaluation."""
        actions = []

        if evaluation_result.get('goal_achieved', False):
            actions.extend([
                "✅ Task completed - consider testing the results",
                "📝 Document the solution for future reference",
                "🔍 Review for potential improvements"
            ])
        else:
            actions.extend([
                "🔄 Retry with refined approach",
                "🔍 Analyze any error messages",
                "💡 Consider alternative solutions"
            ])

        return actions

    def _identify_learning_opportunities(self, evaluation_result: Dict[str, Any]) -> List[str]:
        """Identify learning opportunities from the execution."""
        opportunities = []

        quality_score = evaluation_result.get('quality_score', 0.0)
        if quality_score < 0.8:
            opportunities.append("📚 Study best practices for similar tasks")

        performance_metrics = evaluation_result.get('performance_metrics', {})
        if performance_metrics.get('efficiency_rating') == 'low':
            opportunities.append("⚡ Learn optimization techniques")

        return opportunities

    def _get_affected_files(self, stages_result: Dict[str, Any]) -> List[str]:
        """Extract list of files affected during execution."""
        affected_files = []

        execution_result = stages_result.get('execution', {})
        iterations = execution_result.get('execution_result', {}).get('iterations', [])

        for iteration in iterations:
            phases = iteration.get('phases', {})
            execute_phase = phases.get('execute', {})
            exec_result = execute_phase.get('execution_result', {})

            affected_files.extend(exec_result.get('files_created', []))
            affected_files.extend(exec_result.get('files_modified', []))

        return list(set(affected_files))  # Remove duplicates

    def _extract_performance_metrics(self, stages_result: Dict[str, Any]) -> Dict[str, Any]:
        """Extract performance metrics from execution results."""
        execution_result = stages_result.get('execution', {})
        evaluation_result = stages_result.get('evaluation', {})

        return {
            'total_execution_time': execution_result.get('total_time', 0.0),
            'operations_completed': execution_result.get('operations_completed', 0),
            'iterations_used': len(execution_result.get('iterations', [])),
            'quality_score': evaluation_result.get('quality_score', 0.0),
            'efficiency_rating': evaluation_result.get('performance_metrics', {}).get('efficiency_rating', 'unknown'),
            'goal_achievement_rate': 1.0 if execution_result.get('goal_achieved', False) else 0.0
        }

    def _update_metrics(self, success: bool, execution_time: float) -> None:
        """Update performance metrics."""
        self.execution_metrics['total_operations'] += 1

        if success:
            self.execution_metrics['successful_operations'] += 1
        else:
            self.execution_metrics['failed_operations'] += 1

        # Update average execution time
        total_time = self.execution_metrics['total_execution_time'] + execution_time
        self.execution_metrics['total_execution_time'] = total_time
        self.execution_metrics['average_execution_time'] = total_time / self.execution_metrics['total_operations']

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        total_ops = self.execution_metrics['total_operations']

        return {
            'total_operations': total_ops,
            'successful_operations': self.execution_metrics['successful_operations'],
            'failed_operations': self.execution_metrics['failed_operations'],
            'success_rate': self.execution_metrics['successful_operations'] / max(total_ops, 1),
            'average_execution_time': self.execution_metrics['average_execution_time'],
            'total_execution_time': self.execution_metrics['total_execution_time']
        }
